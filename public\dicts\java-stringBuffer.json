[{"name": "append()", "trans": ["public StringBuffer append(String s):将指定的字符串追加到此字符序列。"]}, {"name": "reverse()", "trans": ["public StringBuffer reverse():将此字符序列用其反转形式取代。"]}, {"name": "delete()", "trans": ["public delete(int start, int end):移除此序列的子字符串中的字符。"]}, {"name": "insert()", "trans": ["public insert(int offset, int i):将 int 参数的字符串表示形式插入此序列中。"]}, {"name": "replace()", "trans": ["replace(int start, int end, String str):使用给定 String 中的字符替换此序列的子字符串中的字符。"]}, {"name": "capacity()", "trans": ["int capacity():返回当前容量。"]}, {"name": "charAt()", "trans": ["char charAt(int index):返回此序列中指定索引处的 char 值。"]}, {"name": "ensureCapacity()", "trans": ["void ensureCapacity(int minimumCapacity):确保容量至少等于指定的最小值。"]}, {"name": "getChars()", "trans": ["void getChars(int srcBegin, int srcEnd, char[] dst, int dstBegin):将字符从此序列复制到目标字符数组 dst。"]}, {"name": "indexOf()", "trans": ["int indexOf(String str):返回第一次出现的指定子字符串在该字符串中的索引。"]}, {"name": "indexOf()", "trans": ["int indexOf(String str, int fromIndex):从指定的索引处开始，返回第一次出现的指定子字符串在该字符串中的索引。"]}, {"name": "lastIndexOf()", "trans": ["int lastIndexOf(String str):返回最右边出现的指定子字符串在此字符串中的索引。"]}, {"name": "lastIndexOf()", "trans": ["int lastIndexOf(String str, int fromIndex):返回 String 对象中子字符串最后出现的位置。"]}, {"name": "length()", "trans": ["int length():返回长度（字符数）。"]}, {"name": "setCharAt()", "trans": ["void setCharAt(int index, char ch):将给定索引处的字符设置为 ch。"]}, {"name": "setLength()", "trans": ["void setLength(int newLength):设置字符序列的长度。"]}, {"name": "subSequence()", "trans": ["CharSequence subSequence(int start, int end):返回一个新的字符序列，该字符序列是此序列的子序列。"]}, {"name": "substring()", "trans": ["String substring(int start):返回一个新的 String，它包含此字符序列当前所包含的字符子序列。"]}, {"name": "substring()", "trans": ["String substring(int start, int end):返回一个新的 String，它包含此序列当前所包含的字符子序列。"]}, {"name": "toString()", "trans": ["String toString():返回此序列中数据的字符串表示形式。"]}]