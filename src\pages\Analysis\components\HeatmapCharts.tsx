import { isOpenDark<PERSON>ode<PERSON>tom } from "@/store";
import { useAtom } from "jotai";
import type { FC } from "react";
import React from "react";
import type { Activity } from "react-activity-calendar";
import { ActivityCalendar } from "react-activity-calendar";
import { Tooltip as ReactTooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";

interface HeatmapChartsProps {
  title: string;
  data: Activity[];
}
// 生成过去一年的默认数据
const defaultData: Activity[] = Array.from({ length: 365 }, (_, i) => {
  const date = new Date();
  date.setDate(date.getDate() - (364 - i));
  return {
    date: date.toISOString().split("T")[0],
    count: 0,
    level: 0,
  };
});

const HeatmapCharts: FC<HeatmapChartsProps> = ({ data, title }) => {
  const [isOpenDarkMode] = useAtom(isOpenDarkModeAtom);

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="text-center text-xl font-bold text-gray-600	dark:text-white">
        {title}
      </div>
      <ActivityCalendar
        fontSize={20}
        blockSize={22}
        blockRadius={7}
        style={{
          padding: "40px 60px 20px 100px",
          color: isOpenDarkMode ? "#fff" : "#000",
        }}
        colorScheme={isOpenDarkMode ? "dark" : "light"}
        data={data.length > 0 ? data : defaultData}
        theme={{
          light: ["#f0f0f0", "#6366f1"],
          dark: ["hsl(0, 0%, 22%)", "#818cf8"],
        }}
        renderBlock={(block, activity) =>
          React.cloneElement(block, {
            "data-tooltip-id": "react-tooltip",
            "data-tooltip-html": `${activity.date} 练习 ${activity.count} 次`,
          })
        }
        showWeekdayLabels={true}
        labels={{
          months: [
            "一月",
            "二月",
            "三月",
            "四月",
            "五月",
            "六月",
            "七月",
            "八月",
            "九月",
            "十月",
            "十一月",
            "十二月",
          ],
          weekdays: ["日", "一", "二", "三", "四", "五", "六"],
          totalCount: "过去一年总计 {{count}} 次",
          legend: {
            less: "少",
            more: "多",
          },
        }}
      />
      <ReactTooltip id="react-tooltip" />
    </div>
  );
};

export default HeatmapCharts;
