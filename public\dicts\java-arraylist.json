[{"name": "add()", "trans": ["add():将元素插入到指定位置的 arraylist 中"]}, {"name": "addAll()", "trans": ["addAll():添加集合中的所有元素到 arraylist 中"]}, {"name": "clear()", "trans": ["clear():删除 arraylist 中的所有元素"]}, {"name": "clone()", "trans": ["clone():复制一份 arraylist"]}, {"name": "contains()", "trans": ["contains():判断元素是否在 arraylist"]}, {"name": "get()", "trans": ["get():通过索引值获取 arraylist 中的元素"]}, {"name": "indexOf()", "trans": ["indexOf():返回 arraylist 中元素的索引值"]}, {"name": "removeAll()", "trans": ["removeAll():删除存在于指定集合中的 arraylist 里的所有元素"]}, {"name": "remove()", "trans": ["remove():删除 arraylist 里的单个元素"]}, {"name": "size()", "trans": ["size():返回 arraylist 里元素数量"]}, {"name": "isEmpty()", "trans": ["isEmpty():判断 arraylist 是否为空"]}, {"name": "subList()", "trans": ["subList():截取部分 arraylist 的元素"]}, {"name": "set()", "trans": ["set():替换 arraylist 中指定索引的元素"]}, {"name": "sort()", "trans": ["sort():对 arraylist 元素进行排序"]}, {"name": "toArray()", "trans": ["toArray():将 arraylist 转换为数组"]}, {"name": "toString()", "trans": ["toString():将 arraylist 转换为字符串"]}, {"name": "ensureCapacity()", "trans": ["ensureCapacity():设置指定容量大小的 arraylist"]}, {"name": "lastIndexOf()", "trans": ["lastIndexOf():返回指定元素在 arraylist 中最后一次出现的位置"]}, {"name": "retainAll()", "trans": ["retainAll():保留 arraylist 中在指定集合中也存在的那些元素"]}, {"name": "containsAll()", "trans": ["containsAll():查看 arraylist 是否包含指定集合中的所有元素"]}, {"name": "trimToSize()", "trans": ["trimToSize():将 arraylist 中的容量调整为数组中的元素个数"]}, {"name": "removeRange()", "trans": ["removeRange():删除 arraylist 中指定索引之间存在的元素"]}, {"name": "replaceAll()", "trans": ["replaceAll():将给定的操作内容替换掉数组中每一个元素"]}, {"name": "removeIf()", "trans": ["removeIf():删除所有满足特定条件的 arraylist 元素"]}, {"name": "forEach()", "trans": ["forEach():遍历 arraylist 中每一个元素并执行特定操作"]}]