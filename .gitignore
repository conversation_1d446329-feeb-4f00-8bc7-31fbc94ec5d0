# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
.yarnrc.yml
.yarn
.eslintcache
package-lock.json
pnpm-lock.yaml
.env
stats.html

.idea
/test-results/
/blob-report/
/playwright/.cache/
playwright-report