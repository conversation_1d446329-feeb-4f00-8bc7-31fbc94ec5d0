{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": ["node", "unplugin-icons/types/react"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
      // "@/assets/*": ["assets/*"],
      // "@/components/*": ["components/*"],
      // "@/hooks/*": ["hooks/*"],
      // "@/pages/*": ["pages/*"],
      // "@/resources/*": ["resources/*"],
      // "@/store/*": ["store/*"],
      // "@/utils/*": ["utils/*"]
    },
    "plugins": [
      {
        "name": "typescript-plugin-css-modules",
        "options": {
          "classnameTransform": "camelCaseOnly"
        }
      }
    ]
  },
  "include": ["src"]
}
