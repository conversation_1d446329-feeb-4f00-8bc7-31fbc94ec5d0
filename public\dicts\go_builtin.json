[{"name": "bool", "trans": ["bool 是布尔值的集合，true 和 false。"]}, {"name": "true", "trans": ["true 和 false 是两个无类型布尔值。"], "usphone": "tru", "ukphone": "truː", "sentences": [{"english": "No, honestly, it’s a true story .", "chinese": "不，真的，这是一个真实的故事。"}, {"english": "Students decide if statements are true or false .", "chinese": "由学生来判断说法正确还是错误。"}, {"english": "It’s generally true to say that fewer people are needed nowadays.", "chinese": "总的来说现在需要的人员少了。"}, {"english": "The results appear to hold true (= still be correct ) for other countries.", "chinese": "这些结果在其他国家似乎同样正确。"}], "detailed_translations": [{"pos": "adj", "chinese": "真实的；正确的", "english": "based on facts and not imagined or invented"}, {"pos": "adv", "chinese": "真实地；准确地", "english": ""}, {"pos": "n", "chinese": "真实；准确", "english": ""}, {"pos": "v", "chinese": "装准", "english": ""}]}, {"name": "false", "trans": ["true 和 false 是两个无类型布尔值。"], "usphone": "fɔls", "ukphone": "fɔːls; fɒls", "sentences": [{"english": "I don’t want to give you any false hopes .", "chinese": "我不想让你有任何虚假的希望。"}, {"english": "The statement gives us a false impression that we understand something when we do not.", "chinese": "这句话使我们产生错觉，以为自己懂了，但其实并没懂。"}, {"english": "false assumptions about people of other cultures", "chinese": "对来自其他文化的人的错误假设"}, {"english": "{\"GLOSS\":[\"a feeling of being safe when you are not really safe\"],\"COLLOINEXA\":[\"a false sense of security\"]}", "chinese": "虚假的安全感"}], "detailed_translations": [{"pos": "adj", "chinese": "错误的；虚伪的；伪造的", "english": "based on incorrect information or ideas"}, {"pos": "adv", "chinese": "欺诈地", "english": ""}]}, {"name": "uint8", "trans": ["uint8 是所有无符号8位整数的集合。范围：0 到 255。"]}, {"name": "uint16", "trans": ["uint16 是所有无符号16位整数的集合。范围：0 到 65535。"]}, {"name": "uint32", "trans": ["uint32 是所有无符号32位整数的集合。范围：0 到 4294967295。"]}, {"name": "uint64", "trans": ["uint64 是所有无符号64位整数的集合。范围：0 到 18446744073709551615。"]}, {"name": "int8", "trans": ["int8 是所有有符号8位整数的集合。范围：-128 到 127。"]}, {"name": "int16", "trans": ["int16 是所有有符号16位整数的集合。范围：-32768 到 32767。"]}, {"name": "int32", "trans": ["int32 是所有有符号32位整数的集合。范围：-2147483648 到 2147483647。"]}, {"name": "int64", "trans": ["int64 是所有有符号64位整数的集合。范围：-9223372036854775808 到 9223372036854775807。"]}, {"name": "float32", "trans": ["float32 是所有IEEE-754标准的32位浮点数的集合。"]}, {"name": "float64", "trans": ["float64 是所有IEEE-754标准的64位浮点数的集合。"]}, {"name": "complex64", "trans": ["complex64 是所有由float32实部和虚部构成的复数的集合。"]}, {"name": "complex128", "trans": ["complex128 是所有由float64实部和虚部构成的复数的集合。"]}, {"name": "string", "trans": ["string 是所有由8位字节组成的字符串的集合，通常但不一定表示UTF-8编码的文本。字符串可以为空，但不可以为nil。string类型的值是不可变的。"], "usphone": "strɪŋ", "ukphone": "strɪŋ", "sentences": [{"english": "Her key hung on a string around her neck.", "chinese": "她的钥匙穿了根绳子挂在脖子上。"}, {"english": "a ball of string", "chinese": "一团细绳"}, {"english": "I need a piece of string to tie this package.", "chinese": "我需要一根绳子来捆这个包裹。"}, {"english": "a string of pearls", "chinese": "一串珍珠"}], "detailed_translations": [{"pos": "n", "chinese": "线，细绳；一串", "english": "a strong thread made of several threads twisted together, used for tying or fastening things"}]}, {"name": "int", "trans": ["int 是至少为32位大小的有符号整数类型。它是一个独立的类型，不是 int32 的别名。"]}, {"name": "uint", "trans": ["uint 是至少为32位大小的无符号整数类型。它是一个独立的类型，不是 uint32 的别名。"]}, {"name": "uintptr", "trans": ["uintptr 是一个足够大以容纳任何指针的位模式的整数类型。"]}, {"name": "byte", "trans": ["byte 是 uint8 的别名，在所有方面都等同于 uint8。它通常用于区分字节值和8位无符号整数值。"], "usphone": "baɪt", "ukphone": "baɪt", "sentences": [{"english": "Each character requires one byte of storage space.", "chinese": "每个字符需要一个字节的储存空间。"}], "detailed_translations": [{"pos": "n", "chinese": "字节；8位元组", "english": "a unit for measuring computer information, equal to eight  bit s (=  the smallest unit on which information is stored on a computer  ) "}]}, {"name": "rune", "trans": ["rune 是 int32 的别名，在所有方面都等同于 int32。它通常用于区分字符值和整数值。"]}, {"name": "any", "trans": ["any 是 interface{} 的别名，在所有方面都等同于 interface{}。"], "usphone": "'ɛni", "ukphone": "'enɪ", "sentences": [{"english": "Have you got any money?", "chinese": "你身上有没有钱？"}, {"english": "Any child who breaks the rules will be punished.", "chinese": "凡违反规定的孩子都要受罚。"}], "detailed_translations": [{"pos": "adj", "chinese": "什么，一些；任何的", "english": "some or even the smallest amount or number"}]}, {"name": "comparable", "trans": ["comparable 是由所有可比较类型（布尔值、数字、字符串、指针、通道、由可比较类型组成的数组、其字段全都是可比较类型的结构体）实现的接口。comparable 接口只能用作类型参数约束，不能用作变量类型。"], "usphone": "'kɑmpərəbl", "ukphone": "'kɒmp(ə)rəb(ə)l", "sentences": [{"english": "A car of comparable size would cost far more abroad.", "chinese": "同样大小的汽车在国外要贵得多。"}], "detailed_translations": [{"pos": "adj", "chinese": "可比较的；类似的", "english": "similar to something else in size, number, quality etc, so that you can make a comparison"}]}, {"name": "iota", "trans": ["iota 是一个预声明的标识符，表示当前常量声明中的无类型整数序数编号（通常在括号中的 const 声明中）。它从零开始索引。"], "usphone": "aɪ'otə", "ukphone": "aɪ'outə", "sentences": [], "detailed_translations": [{"pos": "n", "chinese": " 极少量， 极少", "english": ""}]}, {"name": "nil", "trans": ["nil 是一个预声明的标识符，表示指针、通道、函数、接口、映射或切片类型的零值。"], "usphone": "nɪl", "ukphone": "nɪl", "sentences": [{"english": "The new machine reduced labour costs to almost nil.", "chinese": "新机器把劳动成本几乎降到零。"}], "detailed_translations": [{"pos": "n", "chinese": "无，零", "english": "nothing"}]}, {"name": "Type", "trans": ["Type 仅用于文档目的。它是任何 Go 类型的代替，但对于任何给定的函数调用，表示相同的类型。"]}, {"name": "Type1", "trans": ["Type1 仅用于文档目的。它是任何 Go 类型的代替，但对于任何给定的函数调用，表示相同的类型。"]}, {"name": "IntegerType", "trans": ["IntegerType 仅用于文档目的。它是任何整数类型的代替：int、uint、int8 等。"]}, {"name": "FloatType", "trans": ["FloatType 仅用于文档目的。它是任何浮点类型的代替：float32 或 float64。"]}, {"name": "ComplexType", "trans": ["ComplexType 仅用于文档目的。它是任何复数类型的代替：complex64 或 complex128。"]}, {"name": "append", "trans": ["append 内建函数将元素添加到切片的末尾。如果切片具有足够的容量，则会重新切片以容纳新元素。如果没有足够的容量，将分配一个新的底层数组。append 返回更新后的切片。因此，需要存储 append 的结果，通常在保存切片本身的变量中：   tslice = append(slice, elem1, elem2)  tslice = append(slice, anotherSlice...)  作为特例，允许将字符串追加到字节切片中，如下所示：   tslice = append([]byte( hello  ),  world ...) "], "usphone": "ə'pɛnd", "ukphone": "ə'pend", "sentences": [{"english": "She appended a note at the end of the letter.", "chinese": "她在信件尾部附加了一个说明。"}], "detailed_translations": [{"pos": "v", "chinese": "附加；贴上；盖章", "english": "to add something to a piece of writing"}, {"pos": "n", "chinese": "设置数据文件的搜索路径", "english": ""}]}, {"name": "copy", "trans": ["copy 内建函数将元素从源切片复制到目标切片。 (作为特例，它还会将字节从字符串复制到字节切片。) 源和目标可能重叠。copy 返回复制的元素数，这将是 len(src) 和 len(dst) 中的较小者。"], "usphone": "'kɑ<PERSON>", "ukphone": "'kɒpɪ", "sentences": [{"english": "back-up copies of your files", "chinese": "你的文档的备份"}, {"english": "Could you copy this letter and send it out, please?", "chinese": "你能把这封信复印一份发出去吗？"}], "detailed_translations": [{"pos": "n", "chinese": "抄件", "english": "something that is made to be exactly like another thing"}, {"pos": "v", "chinese": "抄写，复制", "english": "to deliberately make or produce something that is exactly like another thing"}]}, {"name": "delete", "trans": ["delete 内建函数从映射中删除具有指定键（m[key]）的元素。如果 m 为 nil 或不存在这样的元素，则 delete 无效。"], "usphone": "dɪ'lit", "ukphone": "dɪ'liːt", "sentences": [{"english": "His name was deleted from the list.", "chinese": "他的名字从名单上删掉了。"}, {"english": "I deleted the file by mistake.", "chinese": "我误删了这个文件。"}], "detailed_translations": [{"pos": "v", "chinese": "删除", "english": "to remove something that has been written down or stored in a computer"}]}, {"name": "len", "trans": ["len 内建函数根据其类型返回 v 的长度：   t数组：v 中的元素数。   数组指针：*v 中的元素数（即使 v 为 nil）。           切片或映射：v 中的元素数；如果 v 为 nil，则 len(v) 为零。   字符串：v 中的字节数。           通道：通道缓冲区中排队（未读）的元素数；如果 v 为 nil，则 len(v) 为零。          对于某些参数，例如字符串字面值或简单的数组表达式，结果可以是常量。有关详细信息，请参阅 Go 语言规范中的“长度和容量”部分。"]}, {"name": "cap", "trans": ["cap 内建函数根据其类型返回 v 的容量：   t数组：v 中的元素数（与 len(v) 相同）。               数组指针：*v 中的元素数（与 len(v) 相同）。               切片：重新切片时切片可以达到的最大长度；如果 v 为 nil，则 cap(v) 为零。               通道：通道缓冲区容量，以元素为单位；如果 v 为 nil，则 cap(v) 为零。              对于某些参数，例如简单的数组表达式，结果可以是常量。有关详细信息，请参阅 Go 语言规范中的“长度和容量”部分。"], "usphone": "kæp", "ukphone": "kæp", "sentences": [{"english": "a baseball cap", "chinese": "棒球帽"}, {"english": "a bottle cap", "chinese": "瓶盖"}, {"english": "a graceful tower capped with a golden dome", "chinese": "一座漂亮的金色圆顶塔"}], "detailed_translations": [{"pos": "n", "chinese": "便帽,军帽；盖,罩,套", "english": "a type of flat hat that has a curved part sticking out at the front, and is often worn as part of a uniform"}, {"pos": "v", "chinese": "覆盖于…顶端", "english": "to have a particular substance on top"}]}, {"name": "make", "trans": ["make 内建函数分配并初始化类型为切片、映射或通道（仅限）的对象。与 new 不同，第一个参数是类型，而不是值。与 new 不同，make 的返回类型与其参数的类型相同，而不是其指针。结果的规范取决于类型：   t切片：size 指定长度。切片的容量等于其长度。可以提供第二个整数参数以指定不同的容量；它必须不小于长度。例如，make([]int, 0, 10) 分配一个大小为 10 的底层数组，并返回长度为 0、容量为 10 的切片，由此底层数组支持。                   映射：分配一个空映射，足够大以容纳指定数量的元素。可以省略大小，此情况下将分配一个较小的起始大小。                   通道：通道的缓冲区以指定的缓冲区容量进行初始化。如果为零，或省略大小，则通道是无缓冲的。"], "usphone": "mek", "ukphone": "meɪk", "sentences": [{"english": "I’m going to show you how to make a box for your tools.", "chinese": "我来教你怎么做一个箱子存放工具。"}, {"english": "A family of mice had made their nest in the roof.", "chinese": "一窝老鼠在屋顶上安了家。"}, {"english": "She made her own wedding dress.", "chinese": "她的婚纱是自己做的。"}, {"english": "The company has been making quality furniture for over 200 years.", "chinese": "这家公司制作优质家具有两百多年的历史了。"}, {"english": "They met while they were making a film.", "chinese": "他们是在拍电影时认识的。"}, {"english": "I was made to wait four hours before I was examined by a doctor.", "chinese": "我被迫等了四个小时才有一个医生给我做检查。"}], "detailed_translations": [{"pos": "v", "chinese": "做，制造；使", "english": "to produce something, for example by putting the different parts of it together"}]}, {"name": "new", "trans": ["new 内建函数分配内存。第一个参数是类型，不是值，返回的值是指向新分配的零值的指针。"], "usphone": "nu", "ukphone": "njuː", "sentences": [{"english": "the city’s new hospital", "chinese": "市里新建的医院"}, {"english": "the new issue of ‘Time’ magazine", "chinese": "新一期《时代周刊》"}, {"english": "new products on the market", "chinese": "市场上的新产品"}, {"english": "The hardest part of this job is understanding the new technology.", "chinese": "这项工作最难的地方是理解新技术。"}, {"english": "a young man with new ideas", "chinese": "一个有新观念的年轻人"}], "detailed_translations": [{"pos": "adj", "chinese": "新的；新近出现的", "english": "recently made, built, invented, written, designed etc"}]}, {"name": "complex", "trans": ["complex 内建函数从两个浮点值构造一个复数值。实部和虚部必须具有相同的大小，即 float32 或 float64（或可分配给它们）。返回值将是相应的复数类型（float32 为 complex64，float64 为 complex128）。"], "usphone": "ˈkɑmplɛks;kəmˈplɛks", "ukphone": "'kɒmpleks", "sentences": [{"english": "a complex system of highways", "chinese": "复杂的公路网"}, {"english": "Photosynthesis is a highly complex process.", "chinese": "光合作用是一个非常复杂的过程。"}, {"english": "<PERSON> seemed to have an instant understanding of the most complex issues.", "chinese": "再复杂的问题彼得似乎也是一看就明白。"}, {"english": "It was a very complex relationship between two complex people.", "chinese": "这是两个复杂的人之间非常复杂的关系。"}], "detailed_translations": [{"pos": "adj", "chinese": "由许多部分组成的，复合的；复杂的，难懂的", "english": "consisting of many different parts and often difficult to understand"}, {"pos": "n", "chinese": " 综合体， 集合体； 情结， 夸大的情绪反应", "english": "a group of buildings, or a large building with many parts, used for a particular purpose"}]}, {"name": "real", "trans": ["real 内建函数返回复数数 c 的实部。返回值将是与 c 的类型相对应的浮点类型。"], "usphone": "'riəl", "ukphone": "riːl", "sentences": [{"english": "She had never seen a real live elephant before.", "chinese": "她以前从未见过活生生的大象。"}, {"english": "a coat made of real fur", "chinese": "真皮大衣"}, {"english": "Things don’t happen quite that easily in real life.", "chinese": "现实生活中不会动不动就有这样的事情发生。"}], "detailed_translations": [{"pos": "adj", "chinese": "真的；现实的", "english": "something that is real is actually what it seems to be and not false or artificial"}]}, {"name": "imag", "trans": ["imag 内建函数返回复数数 c 的虚部。返回值将是与 c 的类型相对应的浮点类型。"]}, {"name": "close", "trans": ["close 内建函数关闭通道，必须是双向的或只发送的。它只能由发送者执行，永远不会由接收者执行，并且在接收到最后一个发送的值后，会关闭通道。从关闭通道 c 接收到的任何接收将不会阻塞，会返回通道元素的零值。形式如   tx, ok := <-c  也会为已关闭且为空的通道设置 ok 为 false。"], "usphone": "kloz", "ukphone": "kləʊs", "sentences": [{"english": "Would you mind if I closed the window?", "chinese": "我把窗户关上你不介意吧？"}, {"english": "She closed the curtains.", "chinese": "她拉上了窗帘。"}, {"english": "Let me do the car door – it won’t close properly.", "chinese": "我来修一下车门，它关不紧。"}, {"english": "<PERSON> closed her eyes and tried to sleep.", "chinese": "贝丝合上眼睛想睡觉。"}, {"english": "She heard the door close behind her.", "chinese": "她听见门在她身后关上了。"}, {"english": "I will now close the meeting by asking you to join me in a final toast.", "chinese": "现在我要请大家最后干一杯来结束这次会议。"}], "detailed_translations": [{"pos": "v", "chinese": "关，闭；结束", "english": "to shut something in order to cover an opening, or to become shut in this way"}]}, {"name": "panic", "trans": ["panic 内建函数停止当前 Goroutine 的正常执行。当函数 F 调用 panic 时，F 的正常执行立即停止。由 F 推迟执行的任何函数都会按照通常的方式运行，然后 F 返回给其调用者。对于调用者 G，对 F 的调用则像是对 panic 的调用，终止 G 的执行并运行任何推迟的函数。这将一直继续，直到执行 Goroutine 中的所有函数都停止，以相反的顺序进行。在那时，程序将以非零退出码终止。此终止序列称为 panic，可以通过内建函数 recover 来控制。"], "usphone": "'pæn<PERSON>k", "ukphone": "'pæn<PERSON>k", "sentences": [{"english": "Her face distorted in panic.", "chinese": "她的脸因恐慌而变相了。"}, {"english": "The panic-stricken fled in all directions.", "chinese": "惊慌的人群向四面八方逃去。"}, {"english": "They ascribed courage to me for something I did out of sheer panic.", "chinese": "由于我纯粹因惊慌而做的事，他们认为我很有勇气。"}], "detailed_translations": [{"pos": "adj", "chinese": "恐慌的", "english": ""}]}, {"name": "recover", "trans": ["recover 内建函数允许程序管理处于 panic 状态的 Goroutine 的行为。在延迟函数（但不是由它调用的任何函数）中执行 recover 调用会通过恢复正常执行来停止 panic 序列，并检索传递给 panic 调用的错误值。如果在延迟函数之外调用 recover，则不会停止 panic 序列。在这种情况下，或者 Goroutine 不在 panic 状态，或者传递给 panic 的参数为 nil，recover 返回 nil。因此，从 recover 返回的返回值报告 Goroutine 是否处于 panic 状态。"], "usphone": "rɪ'kʌvɚ", "ukphone": "rɪ'kʌvə(r)", "sentences": [{"english": "After a few days of fever, she began to recover.", "chinese": "发烧几天之后，她开始恢复。"}, {"english": "Two bodies were recovered from the wreckage.", "chinese": "残骸中找到了两具尸体"}], "detailed_translations": [{"pos": "v", "chinese": "恢复；重新获得；寻回", "english": "to get better after an illness, accident, shock etc"}]}, {"name": "print", "trans": ["print 内建函数以特定于实现的方式格式化其参数，并将结果写入标准错误。print 用于引导和调试；不保证保留在语言中。"], "usphone": "prɪnt", "ukphone": "prɪnt", "sentences": [{"english": "Over five million copies of the paper are printed every day.", "chinese": "这份报纸每日印量超过五百万。"}, {"english": "When the book was first written no publisher would print it.", "chinese": "这本书刚写成时没有出版商愿意出版。"}, {"english": "There was no print at all on the backs of the tickets.", "chinese": "门票背面没有印任何文字。"}], "detailed_translations": [{"pos": "v", "chinese": "印刷", "english": "to produce many printed copies of a book, newspaper etc"}, {"pos": "n", "chinese": "印刷的文字", "english": "writing that has been printed, for example in books or newspapers"}]}, {"name": "println", "trans": ["println 内建函数以特定于实现的方式格式化其参数，并将结果写入标准错误。参数之间始终添加空格，并附加换行符。println 用于引导和调试；不保证保留在语言中。"]}, {"name": "error", "trans": ["error 内建接口类型是表示错误条件的传统接口，nil 值表示没有错误。"], "usphone": "'ɛrɚ", "ukphone": "'erə", "sentences": [{"english": "There must be an error in our calculations.", "chinese": "我们的计算肯定有错。"}], "detailed_translations": [{"pos": "n", "chinese": "错误，谬误；差错", "english": "a mistake"}]}]