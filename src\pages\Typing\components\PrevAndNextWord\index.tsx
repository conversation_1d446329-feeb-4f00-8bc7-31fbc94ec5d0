import {
  findNextUnfamiliarIndex,
  findPrevUnfamiliarIndex,
  useFamiliarWords,
} from "../../hooks/useFamiliarWords";
import { TypingContext, TypingStateActionType } from "../../store";
import Tooltip from "@/components/Tooltip";
import { currentDictInfoAtom, wordDictationConfigAtom } from "@/store";
import { CTRL } from "@/utils";
import { useAtomValue } from "jotai";
import { useCallback, useContext, useMemo } from "react";
import IconPrev from "~icons/tabler/arrow-narrow-left";
import IconNext from "~icons/tabler/arrow-narrow-right";

export default function PrevAndNextWord({ type }: LastAndNextWordProps) {
  // eslint-disable-next-line  @typescript-eslint/no-non-null-assertion
  const { state, dispatch } = useContext(TypingContext)!;

  const wordDictationConfig = useAtomValue(wordDictationConfigAtom);
  const newIndex = useMemo(
    () => state.chapterData.index + (type === "prev" ? -1 : 1),
    [state.chapterData.index, type]
  );
  const word = state.chapterData.words[newIndex];
  const shortCutKey = useMemo(
    () =>
      type === "prev"
        ? `${CTRL} + Shift + ArrowLeft`
        : `${CTRL} + Shift + ArrowRight`,
    [type]
  );
  const currentLanguage = useAtomValue(currentDictInfoAtom).language;
  const { familiarWords } = useFamiliarWords();

  const onClickWord = useCallback(() => {
    if (!word) return;
    let targetIndex = -1;
    if (type === "prev") {
      targetIndex = findPrevUnfamiliarIndex(
        state.chapterData.words,
        familiarWords,
        state.chapterData.index
      );
    }
    if (type === "next") {
      targetIndex = findNextUnfamiliarIndex(
        state.chapterData.words,
        familiarWords,
        state.chapterData.index
      );
    }
    if (targetIndex !== -1) {
      dispatch({
        type: TypingStateActionType.SKIP_2_WORD_INDEX,
        newIndex: targetIndex,
      });
    }
  }, [
    type,
    dispatch,
    word,
    state.chapterData.words,
    familiarWords,
    state.chapterData.index,
  ]);

  const headWord = useMemo(() => {
    if (!word) return "";

    const showWord = ["romaji", "hapin"].includes(currentLanguage)
      ? word.notation
      : word.name;

    if (type === "prev") return showWord;

    if (type === "next") {
      return !wordDictationConfig.isOpen
        ? showWord
        : (showWord || "").replace(/./g, "_");
    }
  }, [word, currentLanguage, type, wordDictationConfig.isOpen]);

  return (
    <>
      {word ? (
        <Tooltip content={`快捷键: ${shortCutKey}`}>
          <div
            onClick={onClickWord}
            className="flex max-w-xs cursor-pointer select-none items-center text-gray-700 opacity-60 duration-200 ease-in-out hover:opacity-100 dark:text-gray-400"
          >
            {type === "prev" && (
              <IconPrev className="mr-4 shrink-0 grow-0 text-2xl" />
            )}

            <div
              className={`grow-1 flex w-full flex-col ${
                type === "next" ? "items-end text-right" : ""
              }`}
            >
              <p
                className={`font-mono text-2xl font-normal text-gray-700 dark:text-gray-400 ${
                  !wordDictationConfig.isOpen
                    ? "tracking-normal"
                    : "tracking-wider"
                }`}
              >
                {headWord}
              </p>
              {state.isTransVisible && (
                <p className="line-clamp-1 max-w-full text-sm font-normal text-gray-600 dark:text-gray-500">
                  {word.trans.join("；")}
                </p>
              )}
            </div>
            {type === "next" && (
              <IconNext className="ml-4 shrink-0 grow-0 text-2xl" />
            )}
          </div>
        </Tooltip>
      ) : (
        <div />
      )}
    </>
  );
}

export type LastAndNextWordProps = {
  /** 上一个单词还是下一个单词 */
  type: "prev" | "next";
};
