{"name": "keybr", "version": "0.1.0", "private": true, "homepage": ".", "type": "module", "dependencies": {"@floating-ui/react": "^0.20.1", "@headlessui/react": "^1.7.13", "@headlessui/tailwindcss": "^0.1.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.0.2", "@radix-ui/react-radio-group": "^1.1.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slider": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.10.7", "@types/axios": "^0.14.4", "@types/express": "^5.0.1", "animate.css": "^4.1.1", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.6.0", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.0.0", "cors": "^2.8.5", "dayjs": "^1.11.8", "dexie": "^3.2.3", "dexie-export-import": "^4.0.7", "dexie-react-hooks": "^1.1.3", "dotenv": "^16.0.3", "echarts": "^5.4.2", "embla-carousel-react": "^8.2.1", "express": "^5.1.0", "file-saver": "^2.0.5", "howler": "^2.2.3", "html-to-image": "^1.11.11", "i18next": "^23.7.16", "i18next-http-backend": "^2.4.2", "immer": "^9.0.21", "jotai": "^2.0.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "mixpanel-browser": "^2.45.0", "mongoose": "^8.13.2", "pako": "^2.1.0", "react": "^18.3.1", "react-activity-calendar": "^2.0.2", "react-app-polyfill": "^3.0.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-i18next": "^13.5.0", "react-hotkeys-hook": "^4.3.7", "react-timer-hook": "^3.0.5", "react-tooltip": "^5.18.0", "source-map-explorer": "^2.5.2", "swr": "^2.0.4", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "use-debounce": "^10.0.5", "use-immer": "^0.9.0", "use-sound": "^4.0.1", "vike": "^0.4.227", "vike-react": "0.5.13", "xlsx": "^0.18.5", "zustand": "^4.5.0"}, "scripts": {"dev": "vike dev", "start": "vike dev", "build": "vike build", "test": "echo \"No tests\"", "test:e2e": "playwright test", "lint": "eslint .", "prettier": "prettier --write .", "prepare": "husky install", "server": "ts-node src/server/index.ts"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@iconify/json": "^2.2.242", "@playwright/test": "^1.40.1", "@svgr/core": "^7.0.0", "@svgr/plugin-jsx": "^7.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/postcss7-compat": "^2.2.17", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/bcryptjs": "^3.0.0", "@types/canvas-confetti": "^1.6.0", "@types/cors": "^2.8.17", "@types/echarts": "^4.9.18", "@types/file-saver": "^2.0.5", "@types/howler": "^2.2.3", "@types/jsonwebtoken": "^9.0.9", "@types/mixpanel-browser": "^2.38.1", "@types/mongoose": "^5.11.97", "@types/node": "18.14.6", "@types/pako": "^2.0.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "git-last-commit": "^1.0.1", "husky": "^8.0.0", "lint-staged": "^13.1.2", "postcss": "^8.4.21", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.7", "rollup-plugin-visualizer": "^5.9.0", "tailwindcss": "^3.3.1", "ts-node": "^10.9.2", "typescript-plugin-css-modules": "^5.0.1", "unplugin-icons": "^0.16.1", "vite": "^6.2.3"}}