import i18n from "@/i18n";
import { currentLanguageAtom } from "@/store/languageAtom";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

/**
 * i18n调试组件
 * 显示当前i18n状态和可能的问题
 */
export function I18nDebugger() {
  const { t, i18n: i18nInstance } = useTranslation("common");
  const [currentLanguage] = useAtom(currentLanguageAtom);
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const updateDebugInfo = () => {
      setDebugInfo({
        i18nLanguage: i18n.language,
        jotaiLanguage: currentLanguage,
        isInitialized: i18n.isInitialized,
        hasResources: Object.keys(i18n.store.data).length > 0,
        availableNamespaces: Object.keys(i18n.store.data[i18n.language] || {}),
        htmlLang: typeof document !== "undefined" ? document.documentElement.lang : "N/A",
        timestamp: new Date().toLocaleTimeString(),
      });
    };

    // 初始更新
    updateDebugInfo();

    // 监听语言变化
    i18n.on("languageChanged", updateDebugInfo);
    
    // 定期更新（用于检测状态变化）
    const interval = setInterval(updateDebugInfo, 1000);

    return () => {
      i18n.off("languageChanged", updateDebugInfo);
      clearInterval(interval);
    };
  }, [currentLanguage]);

  const testTranslation = (key: string) => {
    try {
      return t(key);
    } catch (error) {
      return `Error: ${error}`;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">i18n Debug Info</h3>
      
      <div className="space-y-1">
        <div>
          <strong>i18n Language:</strong> {debugInfo.i18nLanguage}
        </div>
        <div>
          <strong>Jotai Language:</strong> {debugInfo.jotaiLanguage}
        </div>
        <div>
          <strong>Initialized:</strong> {debugInfo.isInitialized ? "✅" : "❌"}
        </div>
        <div>
          <strong>Has Resources:</strong> {debugInfo.hasResources ? "✅" : "❌"}
        </div>
        <div>
          <strong>HTML Lang:</strong> {debugInfo.htmlLang}
        </div>
        <div>
          <strong>Namespaces:</strong> {debugInfo.availableNamespaces?.join(", ") || "None"}
        </div>
        <div>
          <strong>Updated:</strong> {debugInfo.timestamp}
        </div>
      </div>

      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="text-xs">
          <strong>Translation Tests:</strong>
        </div>
        <div>common:navigation.home = "{testTranslation("navigation.home")}"</div>
        <div>article:history.title = "{testTranslation("article:history.title")}"</div>
      </div>

      <div className="mt-2 pt-2 border-t border-gray-600">
        <button
          type="button"
          onClick={() => {
            console.log("i18n Debug Info:", debugInfo);
            console.log("i18n Store:", i18n.store.data);
          }}
          className="text-xs bg-blue-600 px-2 py-1 rounded"
        >
          Log to Console
        </button>
      </div>
    </div>
  );
}
