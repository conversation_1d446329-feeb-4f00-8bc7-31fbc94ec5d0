import { SoundIcon } from "./SoundIcon";
import usePronunciationSound from "@/hooks/usePronunciation";
import type { Word } from "@/typings";
import { useCallback, useEffect, useImperativeHandle } from "react";
import React from "react";

export const WordPronunciationIcon = React.forwardRef<
  WordPronunciationIconRef,
  { word: Word; lang: string; className?: string; iconClassName?: string }
>(({ word, lang, className, iconClassName }, ref) => {
  const currentWord = () => {
    if (lang === "hapin") {
      if (/[\u0400-\u04FF]/.test(word.notation || "")) {
        // 哈萨克语西里尔文字
        return word.notation || "";
      } else {
        // 哈萨克语老文字
        return word.trans[2];
      }
    } else {
      return word.name;
    }
  };
  const { play, stop, isPlaying } = usePronunciationSound(currentWord());

  const playSound = useCallback(() => {
    stop();
    play();
  }, [play, stop]);

  useEffect(() => {
    return stop;
  }, [word, stop]);

  useImperativeHandle(
    ref,
    () => ({
      play: playSound,
    }),
    [playSound]
  );

  return (
    <SoundIcon
      animated={isPlaying}
      onClick={playSound}
      className={`cursor-pointer text-gray-600 ${className}`}
      iconClassName={iconClassName}
    />
  );
});

WordPronunciationIcon.displayName = "WordPronunciationIcon";

export type WordPronunciationIconRef = {
  play: () => void;
};
