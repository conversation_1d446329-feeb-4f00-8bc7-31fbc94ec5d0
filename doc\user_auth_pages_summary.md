# 页面权限与登录判断总结

本文档旨在梳理 `src/pages` 目录下各个页面对用户登录状态的依赖情况。

---

### **1. 需要登录才能访问或核心功能需要登录的页面**

这些页面或其核心功能必须在用户登录后才能使用。

- **`pages/profile/`**: **是。** 此页面被 `<ProtectedRoute>` 组件包裹，用于展示和管理用户个人资料，必须登录才能访问。
- **`pages/Admin/`**: **是。** 设计上需要管理员权限。虽然服务端的权限检查暂时被注释，但其客户端逻辑中使用了 `useAuth` 钩子，需要认证才能访问。
- **`pages/review/`** (及其子页面 `today/`, `history/`, `practice/`): **是。** 复习功能（如仪表板、历史记录、练习）依赖于多个自定义钩子来获取用户的个人复习数据。这些操作需要用户认证。
- **`pages/ErrorBook/`**: **是（功能上需要）。** 与复习页面类似，错题本功能需要获取用户的个人错题记录，这需要用户登录。
- **`pages/CustomArticle/`**: **是（功能上需要）。** 用户自定义文章的创建、保存和练习功能是和特定用户绑定的，因此需要登录才能使用。
- **`pages/feedback/`**: **提交时需要登录。** 页面本身可能对所有用户可见，但提交反馈时，`FeedbackForm` 组件会检查用户的登录状态。

---

### **2. 根据登录状态显示不同内容的页面**

这些页面对所有用户开放，但会根据用户的登录状态显示不同的界面元素或提供额外功能。

- **`pages/Typing/`**: **部分需要。** 打字练习的核心功能可能对游客开放，但页面右上角的 `UserAuthMenu` 组件会根据登录状态显示用户信息或"登录/注册"按钮。保存成绩、同步配置等高级功能也需要登录。

---

### **3. 认证流程页面**

这些页面是用户认证流程的一部分。

- **`pages/login/`**: **是。** 这是处理用户登录、注册、找回密码等功能的核心页面。
- **`pages/verify-email/`**: **是。** 用于处理新用户邮箱验证流程。

---

### **4. 目前看来不需要登录的页面**

根据当前代码分析，以下页面似乎不包含明确的登录判断或用户权限限制，其内容对所有用户开放：

- `pages/Analysis/`
- `pages/familiar/`
- `pages/FriendLinks/`
- `pages/Gallery/`
- `pages/Mobile/`
- `pages/updates/`
