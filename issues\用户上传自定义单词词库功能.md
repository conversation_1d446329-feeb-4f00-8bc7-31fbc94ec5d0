# 用户上传自定义单词词库功能设计方案

## 功能目标

这个功能的目标是允许用户上传自己的词库文件（Excel、CSV 等格式），创建个性化的单词书，用于英语打字练习。具体价值包括：

1. **个性化学习**：用户可以根据自己的学习需求和兴趣创建专属词库
2. **灵活性提升**：支持专业领域词汇、特定考试词汇等个性化需求
3. **提高用户粘性**：通过允许用户创建和管理自己的内容，增强用户与平台的连接
4. **扩大应用场景**：使应用不仅适用于标准词库学习，也适用于自定义内容学习

## 用户视角

### 完整流程

1. **进入词库管理页面**：

   - 用户在导航菜单中点击"我的词库"或在词库选择页面点击"上传词库"按钮
   - 未登录用户会被引导先登录或注册账号

2. **上传词库文件**：

   - 用户可以选择上传 Excel 的文件
   - 界面提供清晰的格式说明和模板下载选项
   - 文件大小限制为 10MB，超过限制会显示提示信息
   - **快速上传模式**：提供"简易模式"选项，仅要求用户上传单词列(word)，其他字段(如音标、翻译)设为可选，降低新用户使用门槛
   - **向导式引导**：提供交互式上传向导，引导用户下载模板、填写内容并上传，减少格式错误

3. **预览与编辑**：

   - 上传后，系统解析文件并显示预览界面
   - 用户可以在预览界面修改词库名称、描述、分类和标签
   - **默认分类与标签**：系统自动分析词库内容，智能推荐合适的分类(如"考试词汇"、"日常用语")和标签，减少用户手动输入
   - 可以浏览词库内容，并对单词条目进行编辑、删除或添加

4. **格式验证与修正**：

   - 系统自动检查格式问题并提示（如缺少必要字段、格式错误等）
   - 提供自动修正选项和手动修正界面
   - 对于特殊字符或不规范内容，提供修正建议

5. **创建完成**：

   - 用户确认无误后点击"创建词库"按钮
   - 系统显示进度条和创建状态
   - 创建成功后，提示用户并自动跳转到词库详情页

6. **使用自定义词库**：
   - 自定义词库会出现在"我的词库"分类下
   - 用户可以像使用内置词库一样选择、练习自定义词库
   - 练习数据、错误统计等功能与内置词库保持一致

### 格式要求与提示

1. **支持的文件格式**：

   - Excel 文件(.xlsx, .xls)

2. **必要字段**：

   - 单词(word)：必须，英文单词或短语
   - 翻译(trans)：可选，中文解释或翻译
   - 美式音标(usphone)：可选
   - 英式音标(ukphone)：可选
   - 注释(notation)：可选

3. **失败提示**：
   - 文件格式错误：提示"不支持的文件格式，请上传 Excel 文件"
   - 内容解析错误：提示具体行号和错误原因，如"第 15 行单词格式有误"
   - 字段缺失：提示"缺少必要字段'单词'"
   - 文件过大：提示"文件超过 10MB 限制，请分割后上传"
   - 用户未登录：提示"请先登录后再上传词库"

## 前端需求

### 新增界面

1. **词库管理页面**：

   - 路径：`/custom-dictionaries`
   - 显示用户的所有自定义词库列表
   - 提供"上传新词库"按钮功能

2. **词库上传页面**：

   - 路径：`/custom-dictionaries/upload`
   - 文件上传区域（支持拖拽上传）
   - 格式说明和模板下载链接
   - 上传进度显示
   - 增加去重显示，上传词库中若有重复单词，将保留第一条并忽略重复项。
   - **模式选择**：提供"标准模式"和"简易模式"两种上传选项
     - 简易模式：仅要求 word 字段，其他字段可选
     - 标准模式：要求完整字段信息
   - **交互式向导**：实现多步骤引导界面，包括：
     1. 选择上传模式
     2. 下载适合所选模式的模板
     3. 填写指南（带示例和提示）
     4. 文件上传
     5. 错误检查和修正提示

3. **词库预览编辑页面**：

   - 路径：`/custom-dictionaries/preview`
   - 词库基本信息编辑表单
   - 单词列表预览和编辑界面
   - 分页浏览功能
   - 错误提示和修正界面
   - **虚拟滚动**：实现虚拟滚动以优化大量词条的显示性能，仅渲染可视区域内的元素

4. **词库详情页面**：
   - 路径：`/custom-dictionaries/:id`
   - 显示词库详细信息和单词列表
   - 提供编辑、删除、开始练习按钮

### 新增组件

1. **文件上传组件**：

   - 支持 Excel 文件格式上传
   - 提供拖拽上传区域
   - 显示上传进度和状态
   - **上传模式选择器**：简易/标准模式切换界面，带有模式说明
   - **上传向导**：多步骤引导组件，简化用户操作流程

2. **文件解析组件**：

   - 根据文件格式解析内容
   - 处理各种编码和格式问题
   - 提供错误处理和修正功能

3. **词库信息表单**：

   - 词库名称、描述、分类、标签等信息输入
   - 表单验证和错误提示

4. **单词列表编辑器**：

   - 表格形式展示单词列表
   - 支持行内编辑、删除、添加功能
   - 支持批量操作
   - **虚拟滚动实现**：对于成千上万条词条，实现虚拟滚动以提升性能，只渲染可视区域内的元素
   - **高效分页**：结合服务端分页和客户端虚拟滚动，优化大数据集的加载和展示

5. **自定义词库选择器**：
   - 扩展现有词库选择器，增加"我的词库"分类
   - 区分内置词库和自定义词库的展示样式

### 需修改的文件

1. **词库选择组件**：

   - 修改`src/pages/gallery/CategoryDicts.tsx`和相关组件
   - 增加自定义词库分类和展示

2. **全局状态管理**：

   - 在`src/store`目录下添加自定义词库相关的状态原子
   - 整合到现有的词库选择和加载流程

3. **词库加载逻辑**：
   - 修改`src/utils/wordListFetcher.ts`，支持从本地数据库加载自定义词库
   - 区分内置词库和自定义词库的加载方式

## 后端需求

### 数据库设计

1. **自定义词库表**：

```typescript
interface CustomDictionary {
  id: string; // 唯一标识符
  userId: string; // 关联用户ID
  name: string; // 词库名称
  description: string; // 词库描述
  category: string; // 词库分类
  tags: string[]; // 词库标签
  length: number; // 单词数量
  language: string; // 语言类型
  languageCategory: string; // 语言分类
  createdAt: number; // 创建时间
  updatedAt: number; // 更新时间
  isPublic: boolean; // 是否公开
  version: number; // 版本号
}
```

2. **自定义词库单词表**：

```typescript
interface CustomWord {
  id: string; // 唯一标识符
  dictId: string; // 关联词库ID
  name: string; // 单词
  trans?: string; // 翻译
  usphone?: string; // 美式音标
  ukphone?: string; // 英式音标
  notation?: string; // 注释
  index: number; // 在词库中的索引位置
}
```

3. **数据库索引**：

   - 为 `CustomWord` 表创建索引，优化查询性能：

     ```typescript
     // 对dictId创建索引，加速按词库查询单词
     customWords.createIndex("dictId", "dictId");

     // 对name创建索引，支持单词搜索功能
     customWords.createIndex("name", "name");

     // 对dictId和name创建复合索引，优化特定词库内的单词查询
     customWords.createIndex("dictId_name", ["dictId", "name"]);
     ```

   - 这些索引对于处理大型词库（成千上万词条）时尤为重要，能显著提高查询效率

### API 设计

1. **词库管理 API**：

   - `POST /api/custom-dictionaries`：创建新词库
   - `GET /api/custom-dictionaries`：获取用户词库列表
   - `GET /api/custom-dictionaries/:id`：获取词库详情
   - `PUT /api/custom-dictionaries/:id`：更新词库信息
   - `DELETE /api/custom-dictionaries/:id`：删除词库

2. **单词管理 API**：

   - `POST /api/custom-dictionaries/:id/words`：添加单词
   - `GET /api/custom-dictionaries/:id/words`：获取词库单词列表
     - 支持分页参数：`?page=1&pageSize=100`
     - 支持排序参数：`?sortBy=index&order=asc`
     - 支持搜索参数：`?search=keyword`
   - `PUT /api/custom-dictionaries/:id/words/:wordId`：更新单词
   - `DELETE /api/custom-dictionaries/:id/words/:wordId`：删除单词

3. **文件处理 API**：
   - `POST /api/custom-dictionaries/upload`：上传并解析文件
   - `GET /api/custom-dictionaries/template`：下载模板文件

### 存储方案

1. **本地存储**：

   - 使用 IndexedDB 存储自定义词库数据
   - 与现有的单词记录和进度数据集成

2. **云端存储**（对于已登录用户）：
   - 将自定义词库同步到云端数据库
   - 支持多设备同步和备份

## 边界条件处理

### 异常情况

1. **文件过大**：

   - 前端限制上传文件大小为 10MB
   - 对于大型词库，提供分批导入建议
   - 后端设置最大处理单词数限制（如 10000 个）

2. **格式错误**：

   - 提供清晰的错误提示和行号指示
   - 对于常见错误提供自动修正选项
   - 支持手动编辑修正问题

3. **用户未登录**：

   - 允许未登录用户创建临时词库（存储在本地）
   - 提示用户登录以保存和同步词库
   - 登录后自动将临时词库转为永久词库

4. **同名词库**：

   - 检测是否存在同名词库并提示用户修改词库名称
   - 保留历史版本以防意外覆盖

5. **无效内容**：
   - 自动过滤空行和无效内容
   - 对不符合格式要求的内容提供修正建议
   - 支持部分导入（跳过问题内容）

### 安全考虑

1. **文件类型验证**：

   - 前后端双重验证文件类型和内容
   - 防止恶意文件上传

2. **用户权限控制**：
   - 确保用户只能访问和修改自己的词库

## 后续可选扩展功能

### 词库共享

1. **公开词库**：

   - 允许用户将自己的词库设为公开
   - 其他用户可以浏览和订阅公开词库

2. **词库复制**：

   - 支持用户复制他人共享的词库到自己账户
   - 保留原作者信息和来源

3. **词库评分和评论**：
   - 用户可以对公开词库进行评分和评论
   - 提供热门词库排行榜

### 词库管理增强

1. **分类管理**：

   - 支持用户创建自定义分类
   - 将词库组织到不同分类中

2. **标签系统**：

   - 支持为词库添加多个标签
   - 基于标签的词库过滤和搜索

3. **在线编辑**：
   - 提供完整的在线词库编辑功能
   - 支持从零创建新词库

### 数据分析与学习路径

1. **学习数据分析**：

   - 为自定义词库提供与内置词库相同的学习数据分析
   - 生成学习进度报告和错误分析

2. **词库质量提升**：

   - 自动补充缺失的发音和例句

## 实施计划

1. **阶段一：基础功能实现**

   - 实现基本的文件上传和解析功能
   - 创建自定义词库的本地存储和管理
   - 将自定义词库集成到现有词库选择流程

2. **阶段二：用户体验优化**

   - 完善预览和编辑功能
   - 增强错误处理和格式修正能力
   - 优化用户界面和交互流程

3. **阶段三：扩展功能开发**
   - 实现词库共享功能
   - 添加高级管理功能
   - 集成数据分析和学习建议

## 技术实现建议

1. **文件解析**：

   - 使用 SheetJS/xlsx 库处理 Excel 和 CSV 文件
   - 使用 JSON.parse 处理 JSON 文件
   - 自定义解析器处理 TXT 文件

2. **前端框架**：

   - 继续使用现有的 React+TypeScript 技术栈
   - 利用 Tailwind CSS 保持 UI 风格一致性
   - 使用 React Hook Form 处理表单验证
   - **虚拟滚动实现**：
     - 使用 `react-window` 或 `react-virtualized` 库实现高效的虚拟列表
     - 考虑 `react-window-infinite-loader` 结合无限加载功能
     - 支持动态高度的虚拟列表，适应不同内容长度的词条

3. **数据管理**：
   - 使用 Jotai 管理全局状态
   - 使用 Dexie.js 操作 IndexedDB
   - 实现数据同步机制
   - **优化查询性能**：
     - 利用 Dexie.js 的索引能力优化大数据集查询
     - 实现增量加载和局部更新策略
     - 考虑使用 Web Worker 处理大量数据的解析和处理，避免阻塞主线程
