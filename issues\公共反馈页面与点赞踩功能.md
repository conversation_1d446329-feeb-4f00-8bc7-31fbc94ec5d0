# 公共反馈页面与点赞踩功能

## 需求背景

当前反馈系统仅允许管理员查看反馈列表，不支持普通用户查看其他人的反馈，也无法对反馈进行评价。需要添加公共反馈页面，允许所有用户查看反馈内容，并增加点赞/踩功能，让用户参与评价。

## 实施计划

### 一、后端实现

1. **更新反馈数据模型**

   - 在 Feedback 模型中添加`upvotes`和`downvotes`字段，记录点赞和踩的数量
   - 添加`voters`字段，记录投票用户列表（防止重复投票）

2. **增加公共 API 接口**
   - 创建获取公共反馈列表的 API
   - 创建点赞/踩 API
   - 修改现有提交反馈 API 以适应新字段

### 二、前端实现

1. **创建公共反馈页面**

   - 实现反馈列表展示
   - 添加筛选和排序功能
   - 设计反馈卡片组件，包含点赞/踩按钮

2. **实现点赞/踩功能**

   - 设计投票 UI 组件
   - 实现投票逻辑和状态管理
   - 添加用户交互反馈

3. **更新导航和路由**
   - 在导航菜单中添加公共反馈入口
   - 配置新页面路由

### 详细步骤

#### 步骤 1：更新反馈数据模型

- 修改`src/server/models/Feedback.ts`，添加新字段

#### 步骤 2：实现后端 API

- 在`src/server/controllers/feedback.ts`中添加新控制器函数
- 在`src/server/routes/feedback.ts`中注册新路由

#### 步骤 3：添加前端服务

- 在`src/services/feedbackService.ts`中添加新 API 调用函数

#### 步骤 4：创建公共反馈页面

- 创建`src/pages/Feedback/+Page.tsx`
- 实现反馈列表展示和交互功能

#### 步骤 5：实现点赞/踩组件

- 创建`src/components/VoteButtons/index.tsx`
- 实现投票功能和 UI

#### 步骤 6：更新导航和路由

- 更新导航菜单，添加反馈页面入口
