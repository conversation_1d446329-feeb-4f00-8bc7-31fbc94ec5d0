[{"name": "append()", "trans": ["用于在列表末尾添加新的对象。"]}, {"name": "buffer_info()", "trans": ["返回一个元组(address,length)以给出用于存放数组内容的缓冲区元素的当前内存地址和长度"]}, {"name": "byteswap()", "trans": ["更改基础数据的字节顺序"]}, {"name": "count()", "trans": ["用于统计某个元素在列表中出现的次数。"]}, {"name": "extend()", "trans": ["用于在列表末尾一次性追加另一个序列中的多个值（用新列表扩展原来的列表）"]}, {"name": "fromfile()", "trans": ["用于从列表中找出某个值第一个匹配项的索引位置"]}, {"name": "index()", "trans": ["用于从列表中找出某个值第一个匹配项的索引位置"]}, {"name": "insert()", "trans": ["用于将指定对象插入列表的指定位置"]}, {"name": "pop()", "trans": ["用于移除列表中的一个元素(默认最后一个元素),并且返回该元素的值"]}, {"name": "remove()", "trans": ["用于移除列表中某个值的第一个匹配项"]}, {"name": "reverse()", "trans": ["用于反向列表中元素。"]}]