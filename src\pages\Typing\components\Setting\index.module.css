.tab-content {
  @apply flex w-full flex-col items-start justify-start gap-10 overflow-y-auto pb-40 pl-6 pr-9 pt-8;
}

.section {
  @apply flex w-full flex-col items-start gap-4;
}
.section-label {
  @apply pb-0 text-xl font-medium text-gray-600;
}
.section-description {
  @apply -mt-1 pl-4 text-left text-xs font-normal leading-tight text-gray-600;
}

.block {
  @apply flex w-full flex-col items-start gap-2 py-0 pl-4;
}
.block-label {
  @apply font-medium text-gray-600;
}
.switch-block {
  composes: block;
  @apply flex-row items-center justify-between;
}
