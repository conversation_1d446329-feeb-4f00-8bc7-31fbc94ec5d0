const Keyboard = {
  type: "FeatureCollection",
  features: [
    {
      type: "Feature",
      id: "01",
      properties: { name: "Q" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [0, 18],
            [0, 26],
            [10, 26],
            [10, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "02",
      properties: { name: "W" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [11, 18],
            [11, 26],
            [21, 26],
            [21, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "03",
      properties: { name: "<PERSON>" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [22, 18],
            [22, 26],
            [32, 26],
            [32, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "04",
      properties: { name: "R" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [33, 18],
            [33, 26],
            [43, 26],
            [43, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "05",
      properties: { name: "T" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [44, 18],
            [44, 26],
            [54, 26],
            [54, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "06",
      properties: { name: "Y" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [55, 18],
            [55, 26],
            [65, 26],
            [65, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "07",
      properties: { name: "U" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [66, 18],
            [66, 26],
            [76, 26],
            [76, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "08",
      properties: { name: "I" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [77, 18],
            [77, 26],
            [87, 26],
            [87, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "09",
      properties: { name: "O" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [88, 18],
            [88, 26],
            [98, 26],
            [98, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "10",
      properties: { name: "P" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [99, 18],
            [99, 26],
            [109, 26],
            [109, 18],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "11",
      properties: { name: "A" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [5, 9],
            [5, 17],
            [15, 17],
            [15, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "12",
      properties: { name: "S" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [16, 9],
            [16, 17],
            [26, 17],
            [26, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "13",
      properties: { name: "D" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [27, 9],
            [27, 17],
            [37, 17],
            [37, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "14",
      properties: { name: "F" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [38, 9],
            [38, 17],
            [48, 17],
            [48, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "15",
      properties: { name: "G" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [49, 9],
            [49, 17],
            [59, 17],
            [59, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "16",
      properties: { name: "H" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [60, 9],
            [60, 17],
            [70, 17],
            [70, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "17",
      properties: { name: "J" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [71, 9],
            [71, 17],
            [81, 17],
            [81, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "18",
      properties: { name: "K" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [82, 9],
            [82, 17],
            [92, 17],
            [92, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "19",
      properties: { name: "L" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [93, 9],
            [93, 17],
            [103, 17],
            [103, 9],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "20",
      properties: { name: "Z" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [10, 0],
            [10, 8],
            [20, 8],
            [20, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "21",
      properties: { name: "X" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [21, 0],
            [21, 8],
            [31, 8],
            [31, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "22",
      properties: { name: "C" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [32, 0],
            [32, 8],
            [42, 8],
            [42, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "23",
      properties: { name: "V" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [43, 0],
            [43, 8],
            [53, 8],
            [53, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "24",
      properties: { name: "B" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [54, 0],
            [54, 8],
            [64, 8],
            [64, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "25",
      properties: { name: "N" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [65, 0],
            [65, 8],
            [75, 8],
            [75, 0],
          ],
        ],
      },
    },
    {
      type: "Feature",
      id: "26",
      properties: { name: "M" },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [76, 0],
            [76, 8],
            [86, 8],
            [86, 0],
          ],
        ],
      },
    },
  ],
};

export default Keyboard;
