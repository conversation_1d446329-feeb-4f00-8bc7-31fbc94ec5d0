name: Deployment to Github/Gitee pages
on:
  push:
    branches:
      - master
env:
  REACT_APP_DEPLOY_ENV: pages
jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.17.0]
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install Packages
        run: yarn
      - name: Build page
        run: yarn build
      - name: Deploy to gh-pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          deploy_key: ${{ secrets.ACTIONS_DEPLOY_KEY }}
          publish_dir: ./build
      - name: Sync to Gite<PERSON>
        uses: wearerequired/git-mirror-action@master
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE }}
        with:
          source-repo: **************:Kaiyiwing/qwerty-learner.git
          destination-repo: *************:KaiyiWing/qwerty-learner.git
