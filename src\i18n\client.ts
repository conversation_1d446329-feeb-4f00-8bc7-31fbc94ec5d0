import type { SupportedLanguage } from "@/store/languageAtom";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// 内联翻译资源（避免HTTP请求问题）
const resources = {
  zh: {
    common: {
      "navigation": {
        "home": "首页",
        "typing": "打字练习",
        "article": "文章练习",
        "gallery": "词典展示",
        "updates": "更新日志",
        "friendLinks": "友情链接"
      },
      "buttons": {
        "start": "开始",
        "stop": "停止",
        "save": "保存",
        "cancel": "取消"
      },
      "language": {
        "switch": "切换语言",
        "chinese": "中文",
        "english": "English"
      }
    },
    article: {
      "history": {
        "title": "文章列表",
        "userArticles": "自定义文章",
        "officialArticles": "官方文章"
      }
    }
  },
  en: {
    common: {
      "navigation": {
        "home": "Home",
        "typing": "Typing Practice",
        "article": "Article Practice",
        "gallery": "Dictionary Gallery",
        "updates": "Updates",
        "friendLinks": "Friend Links"
      },
      "buttons": {
        "start": "Start",
        "stop": "Stop",
        "save": "Save",
        "cancel": "Cancel"
      },
      "language": {
        "switch": "Switch Language",
        "chinese": "中文",
        "english": "English"
      }
    },
    article: {
      "history": {
        "title": "Article List",
        "userArticles": "Custom Articles",
        "officialArticles": "Official Articles"
      }
    }
  }
};

let isInitialized = false;

export const initClientI18n = async (initialLanguage: SupportedLanguage = "zh") => {
  if (isInitialized) {
    if (i18n.language !== initialLanguage) {
      await i18n.changeLanguage(initialLanguage);
    }
    return i18n;
  }

  console.log("Initializing i18n with language:", initialLanguage);

  await i18n
    .use(initReactI18next)
    .init({
      lng: initialLanguage,
      fallbackLng: "zh",
      debug: false,
      
      ns: ["common", "article"],
      defaultNS: "common",
      
      interpolation: {
        escapeValue: false,
      },
      
      react: {
        useSuspense: false,
      },
      
      resources,
    });

  isInitialized = true;
  console.log("i18n initialized successfully with language:", i18n.language);
  return i18n;
};

export { i18n as default };
