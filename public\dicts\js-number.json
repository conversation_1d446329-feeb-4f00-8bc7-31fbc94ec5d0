[{"name": "Number.EPSILON", "trans": ["Number.EPSILON 属性表示 1 和大于 1 的最小的浮点数（可表示为 Number）的差值。"]}, {"name": "Number.MAX_SAFE_INTEGER", "trans": ["Number.MAX_SAFE_INTEGER 常量表示在 JavaScript 中最大的安全整数（maxinum safe integer)（253 - 1）。"]}, {"name": "Number.MAX_VALUE", "trans": ["Number.MAX_VALUE 属性表示在 JavaScript 里所能表示的最大数值。"]}, {"name": "Number.MIN_SAFE_INTEGER", "trans": ["Number.MIN_SAFE_INTEGER 代表在 JavaScript中最小的安全的integer型数字 (-(253 - 1))."]}, {"name": "Number.MIN_VALUE", "trans": ["Number.MIN_VALUE 属性表示在 JavaScript 中所能表示的最小的正值。"]}, {"name": "Number.NEGATIVE_INFINITY", "trans": ["Number.NEGATIVE_INFINITY 属性表示负无穷大。"]}, {"name": "Number.NaN", "trans": ["Number.NaN 表示“非数字”（Not-A-Number）。和 NaN 相同。"]}, {"name": "Number.POSITIVE_INFINITY", "trans": ["Number.POSITIVE_INFINITY 属性表示正无穷大。"]}, {"name": "Number.isFinite()", "trans": ["Number.isFinite() 方法用来检测传入的参数是否是一个有穷数（finite number）。"]}, {"name": "Number.isInteger()", "trans": ["Number.isInteger() 方法用来判断给定的参数是否为整数。"]}, {"name": "Number.isNaN()", "trans": ["Number.isNaN() 方法确定传递的值是否为 NaN和其类型是 Number。它是原始的全局isNaN()的更强大的版本。"]}, {"name": "Number.isSafeInteger()", "trans": ["Number.isSafeInteger() 方法用来判断传入的参数值是否是一个“安全整数”（safe integer）。一个安全整数是一个符合下面条件的整数："]}, {"name": "Number.parseFloat()", "trans": ["Number.parseFloat() 方法可以把一个字符串解析成浮点数。该方法与全局的 parseFloat() 函数相同，并且处于 ECMAScript 6 规范中（用于全局变量的模块化）。"]}, {"name": "Number.parseInt()", "trans": ["Number.parseInt() 方法可以根据给定的进制数把一个字符串解析成整数。"]}, {"name": "toExponential()", "trans": ["toExponential() 方法以指数表示法返回该数值字符串表示形式。"]}, {"name": "toFixed()", "trans": ["toFixed() 方法使用定点表示法来格式化一个数。"]}, {"name": "toLocaleString()", "trans": ["toLocaleString() 方法返回这个数字在特定语言环境下的表示字符串。"]}, {"name": "toPrecision()", "trans": ["toPrecision() 方法以指定的精度返回该数值对象的字符串表示。"]}, {"name": "toSource()", "trans": ["toSource() 方法返回该对象源码的字符串表示。"]}, {"name": "toString()", "trans": ["toString() 方法返回指定 Number 对象的字符串表示形式。"]}, {"name": "valueOf()", "trans": ["valueOf() 方法返回一个被 Number 对象包装的原始值。"]}, {"name": "Number.toInteger()", "trans": ["Number.toInteger() 用来将参数转换成整数,但该方法的实现已被移除."]}]