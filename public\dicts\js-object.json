[{"name": "Object.assign()", "trans": ["Object.assign() 方法用于将所有可枚举属性的值从一个或多个源对象复制到目标对象。它将返回目标对象。"]}, {"name": "Object.create()", "trans": ["Object.create()方法创建一个新对象，使用现有的对象来提供新创建的对象的__proto__。 （请查看浏览器控制台以获取视觉证据。）"]}, {"name": "Object.defineProperties()", "trans": ["Object.defineProperties() 方法直接在一个对象上定义新的属性或修改现有属性，并返回该对象。"]}, {"name": "Object.defineProperty()", "trans": ["Object.defineProperty() 方法会直接在一个对象上定义一个新属性，或者修改一个对象的现有属性， 并返回这个对象。"]}, {"name": "Object.entries()", "trans": ["The source for this interactive example is stored in a GitHub repository. If you'd like to contribute to the interactive examples project, please clone https://github.com/mdn/interactive-examples and send us a pull request."]}, {"name": "Object.freeze()", "trans": ["Object.freeze() 方法可以冻结一个对象，冻结指的是不能向这个对象添加新的属性，不能修改其已有属性的值，不能删除已有属性，以及不能修改该对象已有属性的可枚举性、可配置性、可写性。也就是说，这个对象永远是不可变的。该方法返回被冻结的对象。"]}, {"name": "Object.getNotifier()", "trans": ["Object.getNotifer() 方法用于创建可人工触发 change 事件的对象，但该方法在浏览器中已被废弃。"]}, {"name": "Object.getOwnPropertyDescriptor()", "trans": ["Object.getOwnPropertyDescriptor() 方法返回指定对象上一个自有属性对应的属性描述符。（自有属性指的是直接赋予该对象的属性，不需要从原型链上进行查找的属性）"]}, {"name": "Object.getOwnPropertyDescriptors()", "trans": ["Object.getOwnPropertyDescriptors() 方法用来获取一个对象的所有自身属性的描述符。"]}, {"name": "Object.getOwnPropertyNames()", "trans": ["Object.getOwnPropertyNames()方法返回一个由指定对象的所有自身属性的属性名（包括不可枚举属性但不包括Symbol值作为名称的属性）组成的数组。"]}, {"name": "Object.getOwnPropertySymbols()", "trans": ["Object.getOwnPropertySymbols() 方法返回一个给定对象自身的所有 Symbol 属性的数组。"]}, {"name": "Object.getPrototypeOf()", "trans": ["Object.getPrototypeOf() 方法返回指定对象的原型（内部[[Prototype]]属性的值）。"]}, {"name": "Object.is()", "trans": ["Object.is()方法判断两个值是否是相同的值。"]}, {"name": "Object.isExtensible()", "trans": ["Object.isExtensible() 方法判断一个对象是否是可扩展的（是否可以在它上面添加新的属性）。"]}, {"name": "Object.isFrozen()", "trans": ["Object.isFrozen()方法判断一个对象是否被冻结。"]}, {"name": "Object.isSealed()", "trans": ["Object.isSealed() 方法判断一个对象是否被密封。"]}, {"name": "Object.keys()", "trans": ["Object.keys() 方法会返回一个由一个给定对象的自身可枚举属性组成的数组，数组中属性名的排列顺序和使用 for...in 循环遍历该对象时返回的顺序一致 。"]}, {"name": "Object.observe()", "trans": ["Object.observe() 方法用于异步地监视一个对象的修改。当对象属性被修改时，方法的回调函数会提供一个有序的修改流。然而，这个接口已经被废弃并从各浏览器中移除。你可以使用更通用的 Proxy 对象替代。"]}, {"name": "Object.preventExtensions()", "trans": ["Object.preventExtensions()方法让一个对象变的不可扩展，也就是永远不能再添加新的属性。"]}, {"name": "eval()", "trans": ["Object.eval() 方法用于在对象的上下文中对 JavaScript 代码字符串求值，但该方法已被移除。"]}, {"name": "hasOwnProperty()", "trans": ["hasOwnProperty() 方法会返回一个布尔值，指示对象自身属性中是否具有指定的属性"]}, {"name": "isPrototypeOf()", "trans": ["isPrototypeOf() 方法用于测试一个对象是否存在于另一个对象的原型链上。"]}, {"name": "propertyIsEnumerable()", "trans": ["propertyIsEnumerable() 方法返回一个布尔值，表示指定的属性是否可枚举。"]}, {"name": "toLocaleString()", "trans": ["toLocaleString() 方法返回一个该对象的字符串表示。此方法被用于派生对象为了特定语言环境的目的（locale-specific purposes）而重载使用。"]}, {"name": "toSource()", "trans": ["toSource()方法返回一个表示对象源代码的字符串。"]}, {"name": "toString()", "trans": ["toString() 方法返回一个表示该对象的字符串。"]}, {"name": "unwatch()", "trans": ["unwatch() 删除一个 watch() 设置的 watchpoint."]}, {"name": "valueOf()", "trans": ["valueOf() 方法返回指定对象的原始值。"]}, {"name": "watch()", "trans": [""]}, {"name": "Object.seal()", "trans": ["The source for this interactive example is stored in a GitHub repository. If you'd like to contribute to the interactive examples project, please clone https://github.com/mdn/interactive-examples and send us a pull request."]}, {"name": "Object.setPrototypeOf()", "trans": ["如果对象的[[Prototype]]被修改成不可扩展(通过 Object.isExtensible()查看)，就会抛出 TypeError异常。如果prototype参数不是一个对象或者null(例如，数字，字符串，boolean，或者 undefined)，则什么都不做。否则，该方法将obj的[[Prototype]]修改为新的值。"]}, {"name": "Object.unobserve()", "trans": ["Object.unobserve() 是用来移除通过 Object.observe()设置的观察者的方法。"]}, {"name": "Object.values()", "trans": ["Object.values()方法返回一个给定对象自己的所有可枚举属性值的数组，值的顺序与使用for...in循环的顺序相同 ( 区别在于 for-in 循环枚举原型链中的属性 )。"]}, {"name": "apply()", "trans": ["apply() 方法调用一个函数, 其具有一个指定的this值，以及作为一个数组（或类似数组的对象）提供的参数。"]}, {"name": "bind()", "trans": ["bind()方法创建一个新的函数, 当被调用时，将其this关键字设置为提供的值，在调用新函数时，在任何提供之前提供一个给定的参数序列。"]}, {"name": "call()", "trans": ["call() 方法调用一个函数, 其具有一个指定的this值和分别地提供的参数(参数的列表)。"]}, {"name": "isGenerator()", "trans": ["判断一个函数是否是一个生成器."]}]