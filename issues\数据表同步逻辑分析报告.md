# 数据表同步逻辑分析报告

## 概述

检查所有数据表的数据库唯一约束与同步逻辑查询条件的匹配情况，识别潜在的重复键错误风险。

## 分析结果

### 1. WordRecord ✅ 已正确处理

- **MongoDB 唯一约束**: `{ userId: 1, dict: 1, word: 1 }`
- **同步查询条件**: `{ userId, dict, word }` (特殊处理)
- **状态**: 正常，已有专门的处理逻辑

### 2. WordReviewRecord ✅ 已修复

- **MongoDB 唯一约束**: `{ userId: 1, word: 1 }`
- **同步查询条件**: `{ userId, word }` (已修复)
- **状态**: 已修复，添加了特殊处理逻辑

### 3. FamiliarWord ⚠️ 存在潜在问题

- **MongoDB 唯一约束**: `{ userId: 1, dict: 1, word: 1 }`
- **客户端唯一约束**: `[dict+word]`
- **同步查询条件**: `{ userId, uuid }` (通用逻辑)
- **风险**: 高 - 约束不匹配，可能导致重复键错误

### 4. ChapterRecord ✅ 正常

- **MongoDB 唯一约束**: 无特殊业务唯一约束
- **同步查询条件**: `{ userId, uuid }` (通用逻辑)
- **状态**: 正常，基于 UUID 的唯一性

### 5. ReviewRecord ✅ 正常

- **MongoDB 唯一约束**: 无特殊业务唯一约束
- **同步查询条件**: `{ userId, uuid }` (通用逻辑)
- **状态**: 正常，基于 UUID 的唯一性

### 6. ReviewHistory ✅ 正常

- **MongoDB 唯一约束**: 无特殊业务唯一约束
- **同步查询条件**: `{ userId, uuid }` (通用逻辑)
- **状态**: 正常，基于 UUID 的唯一性

### 7. ReviewConfig ✅ 正常

- **MongoDB 唯一约束**: `{ userId: 1 }` (sparse)
- **同步查询条件**: `{ userId, uuid }` (通用逻辑)
- **状态**: 正常，每个用户只有一个配置

## 问题详细分析

### FamiliarWord 表的问题

**问题描述**:

- MongoDB 约束: `{ userId: 1, dict: 1, word: 1 }` - 每个用户每个词典每个单词只能有一个熟词记录
- 客户端约束: `[dict+word]` - 每个词典每个单词只能有一个记录（不区分用户）
- 同步查询: `{ userId, uuid }` - 基于用户 ID 和 UUID 查询

**风险场景**:

1. 客户端可能为同一个 `(dict, word)` 组合创建多个不同 UUID 的记录
2. 同步时使用 `{ userId, uuid }` 查询，找不到现有记录
3. 尝试创建新记录时违反 MongoDB 的 `{ userId: 1, dict: 1, word: 1 }` 约束
4. 抛出 E11000 重复键错误

**客户端创建逻辑检查**:

```typescript
// src/utils/db/index.ts - useMarkFamiliarWord
const existingRecord = await db.familiarWords
  .where("[dict+word]")
  .equals([dict, word])
  .first();
```

客户端正确使用了 `[dict+word]` 查询，但仍可能在并发情况下出现问题。

## 建议修复方案

### FamiliarWord 表修复

需要为 `familiarWords` 添加特殊处理逻辑，类似于 `wordRecords` 和 `wordReviewRecords`：

1. **修改同步查询条件**: 使用 `{ userId, dict, word }` 替代 `{ userId, uuid }`
2. **添加特殊处理逻辑**: 在 syncController.ts 中添加 familiarWords 的专门处理
3. **保持数据一致性**: 确保现有记录的正确更新和新记录的创建

## 优先级评估

- **高优先级**: FamiliarWord - 存在明确的约束不匹配问题
- **低优先级**: 其他表 - 当前逻辑正常工作

## 总结

除了 FamiliarWord 表存在潜在的重复键错误风险外，其他表的同步逻辑都是正常的。建议优先修复 FamiliarWord 表的同步逻辑，以避免类似 WordReviewRecord 的重复键错误。
