import { LanguageSwitcher, LanguageSwitcherCompact } from "@/components/LanguageSwitcher";
import Layout from "@/components/Layout";
import { useI18n } from "@/hooks/useI18n";
import { useTranslation } from "react-i18next";

export function Page() {
  const { t } = useTranslation("common");
  const { currentLanguage } = useI18n();

  // 调试信息
  console.log("Current language from useI18n:", currentLanguage);
  console.log("Translation test:", t("navigation.home"));

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-center">
            i18n 测试页面 / i18n Test Page
          </h1>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">语言切换器 / Language Switcher</h2>
            <div className="space-y-4">
              <div className="flex justify-center">
                <LanguageSwitcher />
              </div>
              <div className="flex justify-center">
                <LanguageSwitcherCompact />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">当前状态 / Current Status</h2>
            <div className="space-y-2">
              <p><strong>当前语言 / Current Language:</strong> {currentLanguage}</p>
              <p><strong>HTML Lang:</strong> {typeof document !== "undefined" ? document.documentElement.lang : "N/A"}</p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">翻译测试 / Translation Test</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">导航 / Navigation</h3>
                <ul className="space-y-1 text-sm">
                  <li>{t("navigation.home")}</li>
                  <li>{t("navigation.typing")}</li>
                  <li>{t("navigation.article")}</li>
                  <li>{t("navigation.gallery")}</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">按钮 / Buttons</h3>
                <div className="space-x-2">
                  <button type="button" className="px-3 py-1 bg-blue-500 text-white rounded text-sm">
                    {t("buttons.start")}
                  </button>
                  <button type="button" className="px-3 py-1 bg-gray-500 text-white rounded text-sm">
                    {t("buttons.stop")}
                  </button>
                  <button type="button" className="px-3 py-1 bg-green-500 text-white rounded text-sm">
                    {t("buttons.save")}
                  </button>
                  <button type="button" className="px-3 py-1 bg-red-500 text-white rounded text-sm">
                    {t("buttons.cancel")}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">使用说明 / Instructions</h2>
            <div className="prose dark:prose-invert max-w-none">
              <ol className="list-decimal list-inside space-y-2">
                <li>点击上方的语言切换器测试语言切换功能</li>
                <li>观察页面文本是否正确切换</li>
                <li>检查HTML lang属性是否同步更新</li>
                <li>测试页面刷新后语言状态是否保持</li>
              </ol>
              
              <hr className="my-4" />
              
              <ol className="list-decimal list-inside space-y-2">
                <li>Click the language switcher above to test language switching</li>
                <li>Observe if page text switches correctly</li>
                <li>Check if HTML lang attribute updates synchronously</li>
                <li>Test if language state persists after page refresh</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
