{"name": "vike-qwerty-server", "version": "1.0.0", "description": "Keybr 后端服务器", "main": "index.ts", "scripts": {"start": "ts-node index.ts", "dev": "nodemon --exec ts-node index.ts", "build": "tsc", "serve": "node dist/index.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "18.14.6", "@types/nodemailer": "^6.4.17", "nodemon": "^3.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}