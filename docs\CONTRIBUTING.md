# 开源代码贡献准则

## PR 来源

1. 我们会在 GitHub 上将需要完成的 feature 和修复的 bug 标记为 "Help Wanted"
2. 用户在用户社群（在项目官方部署的 footer 中有二维码）提出的需求和 bug
3. 根据大家对项目的理解和兴趣，认为需要做的 feature 和修复的 bug

## 在开始做 PR 之前

1. 在 **开发者社群或 issue 区** 进行讨论，确认这个 PR 符合项目需求，并考虑对现有代码和未来计划的影响
2. 在 GitHub 上创建相关的 issue（已有 issue 则无须创建），并进行回复。尽可能在 issue 描述了问题、解决方案、相关细节和贡献者预期的工作，方便大家进行讨论
3. 确认开始 issue 后，尽可能在一周内解决（相对复杂的 issue 除外），以免 PR 长期无法推进，其他开发者也无法参与

## 在 PR 过程中

1. 在开始 coding 后，尽早提出一个 draft PR，方便其他开发者参与讨论，给出建议和帮助
2. 遇到任何技术问题和实现路线问题，可以在 开发者社群或 issue 区 进行讨论。确保 PR 满足项目的开发规范和质量标准，经过充分测试和文档支持
3. 在 PR 中友好协作，回应 Code Review。接受反馈并进行改进，遵守代码风格和注释规范，确保代码的可读性和可维护性
4. 其他人也可以对 PR 进行 Review，帮助发现代码中的问题，提出自己的建议和想法

不需要担心自己的 pr 没有完整的覆盖掉所有 corner case 或者是否会与项目的其他功能产生冲突（对于 new contributor 来说，很难有时间完整理解项目所有代码和逻辑，so take easy ❤️），只需要尽自己所能去实现，我们会在 review 的过程中一起讨论和完善。 我们都贡献自己擅长的部分就可以打造一个优秀 pr 和更好的 Qwerty！

## 完成 PR 后

1. 标记相关 issue 为完成。确保代码被合并到主分支，并在生产环境中经过充分测试和部署
2. 如果是用户社群的相关需求，可以在社群内对用户进行回复

## 行为准则

1. 尊重所有贡献者，不论其技术水平、经验、性别、性取向、种族、宗教信仰或国籍
2. 保持开放的心态，愿意接受其他人的批评和建议，并根据反馈进行改进
3. 提交有价值的贡献，符合项目需求并遵守项目的开发规范和质量标准
4. 不要在 PR 或讨论中使用不礼貌或侮辱性的语言，不要发布任何垃圾或攻击性的内容
5. 遵守代码行为准则，不要进行不道德或不法的行为，如抄袭、篡改他人代码、恶意破坏等
6. 避免进行灌水式的讨论或评论，尽量保持话题与项目相关，并尊重他人的意见和观点
7. 遵守相关法律法规和 GitHub 平台的规定，不发布含有违法、政治或淫秽内容的 PR 或评论

## 反馈

如有任何问题或建议，请随时在 issue 区或者群内提出，我们将及时解决。同时，欢迎参与项目的讨论和开发，共同打造更加优秀的开源项目！

## 写在最后

不要害怕提出问题，不要害怕提出 PR，不要害怕提出建议，不要害怕提出想法，不要害怕提出质疑，不要害怕提出帮助，不要害怕提出改进

![kai-fu](./kai-fu.png)
