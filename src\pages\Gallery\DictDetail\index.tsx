import { useDeleteWordRecord } from "../../../utils/db";
import Chapter from "../Chapter";
import { ErrorTable } from "../ErrorTable";
import { getRowsFromErrorWordData } from "../ErrorTable/columns";
import { ReviewDetail } from "../ReviewDetail";
import useErrorWordData from "../hooks/useErrorWords";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  currentChapterAtom,
  currentDictIdAtom,
  reviewModeInfoAtom,
} from "@/store";
import type { Dictionary } from "@/typings";
import range from "@/utils/range";
import { useAtom, useSetAtom } from "jotai";
import { useCallback, useMemo, useState } from "react";
import IcOutlineCollectionsBookmark from "~icons/ic/outline-collections-bookmark";
import MajesticonsPaperFoldTextLine from "~icons/majesticons/paper-fold-text-line";
import PajamasReviewList from "~icons/pajamas/review-list";

enum Tab {
  Chapters = "chapters",
  Errors = "errors",
  Review = "review",
}

export default function DictDetail({
  dictionary: dict,
  onOpenChange,
}: {
  dictionary: Dictionary;
  onOpenChange?: (open: boolean) => void;
}) {
  const [currentChapter, setCurrentChapter] = useAtom(currentChapterAtom);
  const [currentDictId, setCurrentDictId] = useAtom(currentDictIdAtom);
  const [curTab, setCurTab] = useState<Tab>(Tab.Chapters);
  const setReviewModeInfo = useSetAtom(reviewModeInfoAtom);
  const { deleteWordRecord } = useDeleteWordRecord();
  const [reload, setReload] = useState(false);

  const chapter = useMemo(
    () => (dict.id === currentDictId ? currentChapter : 0),
    [currentChapter, currentDictId, dict.id]
  );
  const { errorWordData, isLoading, error } = useErrorWordData(dict, reload);

  const tableData = useMemo(() => {
    return getRowsFromErrorWordData(errorWordData);
  }, [errorWordData]);

  const onDelete = useCallback(
    async (word: string) => {
      await deleteWordRecord(word, dict.id);
      setReload((old) => !old);
    },
    [deleteWordRecord, dict.id]
  );

  const onChangeChapter = useCallback(
    (index: number) => {
      setCurrentDictId(dict.id);
      setCurrentChapter(index);
      setReviewModeInfo((old) => ({ ...old, isReviewMode: false }));
      onOpenChange?.(false);
      window.location.href = "/";
    },
    [
      dict.id,
      setCurrentChapter,
      setCurrentDictId,
      setReviewModeInfo,
      onOpenChange,
    ]
  );

  const handleTabChange = useCallback(
    (value: Tab) => {
      if (value !== curTab) {
        setCurTab(value);
      }
    },
    [curTab]
  );

  return (
    <div className="flex flex-col rounded-[4rem] px-4 py-3 pl-5 text-gray-800 dark:text-gray-300">
      <div className="text relative flex h-40 flex-col gap-2">
        <h3 className="text-2xl font-semibold">{dict.name}</h3>
        <p className="mt-1">{dict.chapterCount} 章节</p>
        <p>共 {dict.length} 词</p>
        <p>{dict.description}</p>
        <div className="absolute bottom-5 right-4">
          <ToggleGroup
            type="single"
            value={curTab}
            onValueChange={handleTabChange}
          >
            <ToggleGroupItem
              value={Tab.Chapters}
              disabled={curTab === Tab.Chapters}
              className={`${
                curTab === Tab.Chapters
                  ? "text-primary-foreground bg-primary"
                  : ""
              } disabled:opacity-100`}
            >
              <MajesticonsPaperFoldTextLine className="mr-1.5 text-gray-500" />
              章节选择
            </ToggleGroupItem>
            {errorWordData.length > 0 && (
              <>
                <ToggleGroupItem
                  value={Tab.Errors}
                  disabled={curTab === Tab.Errors}
                  className={`${
                    curTab === Tab.Errors
                      ? "text-primary-foreground bg-primary"
                      : ""
                  } disabled:opacity-100`}
                >
                  <IcOutlineCollectionsBookmark className="mr-1.5 text-gray-500" />
                  查看错题
                </ToggleGroupItem>
                <ToggleGroupItem
                  value={Tab.Review}
                  disabled={curTab === Tab.Review}
                  className={`${
                    curTab === Tab.Review
                      ? "text-primary-foreground bg-primary"
                      : ""
                  } disabled:opacity-100`}
                >
                  <PajamasReviewList className="mr-1.5 text-gray-500" />
                  错题回顾
                </ToggleGroupItem>
              </>
            )}
          </ToggleGroup>
        </div>
      </div>
      <div className="flex pl-0">
        <Tabs value={curTab} className="h-[30rem] w-full ">
          <TabsContent value={Tab.Chapters} className="h-full ">
            <ScrollArea className="h-[30rem] ">
              <div className="flex w-full flex-wrap gap-3">
                {range(0, dict.chapterCount, 1).map((index) => (
                  <Chapter
                    key={`${dict.id}-${index}`}
                    index={index}
                    checked={chapter === index}
                    dictID={dict.id}
                    onChange={onChangeChapter}
                  />
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          <TabsContent value={Tab.Errors} className="h-full">
            <ErrorTable
              data={tableData}
              isLoading={isLoading}
              error={error}
              onDelete={onDelete}
            />
          </TabsContent>
          <TabsContent value={Tab.Review} className="h-full">
            <ReviewDetail errorData={errorWordData} dict={dict} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
