import i18n from "@/i18n";
import {
  type SupportedLanguage,
  currentLanguageAtom,
  languageNames,
  supportedLanguages,
} from "@/store/languageAtom";
import { useAtom } from "jotai";
import { Globe } from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface LanguageSwitcherProps {
  className?: string;
  showLabel?: boolean;
}

/**
 * 语言切换组件
 * 支持中文和英文切换，无刷新切换体验
 */
export function LanguageSwitcher({
  className = "",
  showLabel = true,
}: LanguageSwitcherProps) {
  const { t } = useTranslation("common");
  const [currentLanguage, setCurrentLanguage] = useAtom(currentLanguageAtom);
  const [isReady, setIsReady] = useState(false);

  // 检查i18n是否已准备好
  useEffect(() => {
    // 简单检查i18n是否已初始化
    const checkI18nReady = () => {
      if (i18n.isInitialized) {
        setIsReady(true);
      } else {
        // 如果没有初始化，等待一小段时间后再检查
        setTimeout(checkI18nReady, 100);
      }
    };

    checkI18nReady();
  }, []);

  // 如果i18n还没准备好，显示loading状态
  if (!isReady) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showLabel && (
          <div className="flex items-center space-x-1 text-sm text-gray-400 dark:text-gray-600">
            <Globe size={16} className="animate-pulse" />
            <span>Loading...</span>
          </div>
        )}
        <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          <div className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse">
            ...
          </div>
        </div>
      </div>
    );
  }

  const handleLanguageChange = async (newLanguage: SupportedLanguage) => {
    if (newLanguage === currentLanguage) return;

    console.log("Changing language from", currentLanguage, "to", newLanguage);

    try {
      // 切换i18next语言
      await i18n.changeLanguage(newLanguage);

      // 更新Jotai状态
      setCurrentLanguage(newLanguage);

      // 更新HTML lang属性
      if (typeof document !== "undefined") {
        document.documentElement.lang = newLanguage === "zh" ? "zh-CN" : "en";
      }

      console.log("Language changed successfully to", newLanguage);
    } catch (error) {
      console.error("Failed to change language:", error);
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
          <Globe size={16} />
          <span>{t("language.switch")}</span>
        </div>
      )}

      <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        {supportedLanguages.map((language) => (
          <button
            key={language}
            type="button"
            onClick={() => handleLanguageChange(language)}
            className={`
              px-3 py-1 text-sm font-medium rounded-md transition-all duration-200
              ${
                currentLanguage === language
                  ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700"
              }
            `}
            aria-label={`Switch to ${languageNames[language]}`}
          >
            {languageNames[language]}
          </button>
        ))}
      </div>
    </div>
  );
}

/**
 * 简化版语言切换组件（只显示图标）
 */
export function LanguageSwitcherCompact({
  className = "",
}: {
  className?: string;
}) {
  const { t } = useTranslation("common");
  const [currentLanguage, setCurrentLanguage] = useAtom(currentLanguageAtom);
  const [isReady, setIsReady] = useState(false);

  // 检查i18n是否已准备好
  useEffect(() => {
    const checkI18nReady = () => {
      if (i18n.isInitialized) {
        setIsReady(true);
      } else {
        setTimeout(checkI18nReady, 100);
      }
    };

    checkI18nReady();
  }, []);

  // 如果i18n还没准备好，显示loading状态
  if (!isReady) {
    return (
      <div
        className={`
          flex items-center justify-center w-12 h-10 rounded-lg
          bg-gray-100 dark:bg-gray-800
          text-gray-400 dark:text-gray-600 ${className}
        `}
      >
        <Globe size={14} className="animate-pulse" />
      </div>
    );
  }

  const handleToggleLanguage = async () => {
    const newLanguage: SupportedLanguage =
      currentLanguage === "zh" ? "en" : "zh";

    console.log("Switching language from", currentLanguage, "to", newLanguage);

    try {
      await i18n.changeLanguage(newLanguage);
      setCurrentLanguage(newLanguage);

      // 更新HTML lang属性
      if (typeof document !== "undefined") {
        document.documentElement.lang = newLanguage === "zh" ? "zh-CN" : "en";
      }

      console.log("Language switched successfully to", newLanguage);
    } catch (error) {
      console.error("Failed to toggle language:", error);
    }
  };

  return (
    <button
      type="button"
      onClick={handleToggleLanguage}
      className={`
        flex items-center justify-center w-12 h-10 rounded-lg
        bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700
        text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white
        transition-all duration-200 relative ${className}
      `}
      title={`${t("language.switch")} (${currentLanguage.toUpperCase()})`}
      aria-label={`${t("language.switch")} (${currentLanguage.toUpperCase()})`}
    >
      <div className="flex flex-col items-center">
        <Globe size={14} />
        <span className="text-xs font-medium mt-0.5">
          {currentLanguage.toUpperCase()}
        </span>
      </div>
    </button>
  );
}
